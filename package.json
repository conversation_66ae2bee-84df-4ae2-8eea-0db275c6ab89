{"name": "ablegod-backend", "version": "1.0.0", "main": "api/index.js", "scripts": {"start": "node api/index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@react-email/components": "^0.0.33", "@react-email/render": "^1.0.5", "axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "googleapis": "^144.0.0", "mongoose": "^8.0.0", "nodemailer": "^6.10.0", "socket.io": "^4.8.1", "xml": "^1.0.1"}}